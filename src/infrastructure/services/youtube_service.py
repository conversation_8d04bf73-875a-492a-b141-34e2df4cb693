"""YouTube service implementation using FastSaver API only."""
import logging
from typing import Optional
import httpx
import re

from src.domain.interfaces.services import IYouTubeService
from src.domain.entities.download_result import DownloadResult
from src.domain.value_objects.url import Url
from src.infrastructure.config.settings import settings

logger = logging.getLogger(__name__)


class YouTubeService(IYouTubeService):
    """YouTube service implementation using FastSaver API only."""

    def __init__(self):
        # Always use FastSaver API
        pass

    async def get_media_info(self, url: Url):
        """Get media information from URL."""
        # This method is required by the interface but not used for YouTube
        # YouTube downloads are handled directly
        raise NotImplementedError("Use download_video method for YouTube")

    async def download_media(self, media_item):
        """Download media content."""
        # This method is required by the interface but not used for YouTube
        # YouTube downloads are handled directly
        raise NotImplementedError("Use download_video method for YouTube")

    async def download_video(self, url: Url, bot_username: str = "instasaver_bot", video_format: Optional[str] = None) -> DownloadResult:
        """Download a YouTube video using FastSaver API."""
        return await self._download_via_fastsaver_api(url, "video", bot_username, video_format)

    async def download_audio(self, url: Url, bot_username: str = "instasaver_bot", video_format: Optional[str] = None) -> DownloadResult:
        """Download a YouTube video as audio using FastSaver API."""
        return await self._download_via_fastsaver_api(url, "audio", bot_username, video_format)

    async def _get_video_title(self, video_id: str) -> str:
        """Get video title from YouTube page."""
        try:
            # Try to get title from YouTube page
            url = f"https://www.youtube.com/watch?v={video_id}"
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(url)
                response.raise_for_status()

                # Extract title from page HTML
                html = response.text
                title_match = re.search(r'"title":"([^"]+)"', html)
                if title_match:
                    title = title_match.group(1)
                    # Decode unicode escapes
                    title = title.encode().decode('unicode_escape')
                    return title

                # Fallback: try another pattern
                title_match = re.search(r'<title>([^<]+)</title>', html)
                if title_match:
                    title = title_match.group(1)
                    # Remove " - YouTube" suffix
                    title = title.replace(' - YouTube', '')
                    return title

        except Exception as e:
            logger.warning(f"Failed to get video title for {video_id}: {e}")

        return None  # Return None if title not found



    async def _download_via_fastsaver_api(self, url: Url, media_type: str = "video", bot_username: str = "instasaver_bot", video_format: Optional[str] = None) -> DownloadResult:
        """Download YouTube media using FastSaver API."""
        try:
            # Extract video ID from URL
            video_id = url.extract_youtube_video_id()
            if not video_id:
                return DownloadResult(
                    success=False,
                    message="Could not extract video ID from URL"
                )

            # Determine format based on media_type
            if media_type == "audio":
                format_param = "mp3"
                # Validate that audio format is not being used for video
                if video_format and video_format != "mp3":
                    logger.warning(f"Invalid format {video_format} for audio download, using mp3")
            else:
                # Use provided format or default from config
                format_param = video_format or settings.youtube.default_video_format

                # Validate that mp3 is not being used for video
                if format_param == "mp3":
                    logger.warning("mp3 format cannot be used for video download, using default video format")
                    format_param = settings.youtube.default_video_format

                # Validate format is supported
                if format_param not in settings.youtube.available_formats:
                    logger.warning(f"Unsupported video format: {format_param}, using default: {settings.youtube.default_video_format}")
                    format_param = settings.youtube.default_video_format

            # Prepare API request
            params = {
                "video_id": video_id,
                "format": format_param,
                "bot_username": bot_username,
                "token": settings.youtube.fastsaver_api_token
            }

            logger.info(f"Downloading via FastSaver API: {video_id}, format: {format_param}")

            # Try the requested format first, then fallback to 720p if it fails
            formats_to_try = [format_param]
            if format_param != "720p" and media_type == "video":
                formats_to_try.append("720p")

            last_error = None
            for attempt_format in formats_to_try:
                try:
                    params["format"] = attempt_format
                    logger.info(f"Trying format: {attempt_format}")

                    async with httpx.AsyncClient(timeout=60.0) as client:
                        response = await client.get(settings.youtube.fastsaver_download_url, params=params)
                        response.raise_for_status()

                        data = response.json()

                        # Check for API errors
                        if data.get("error", False):
                            error_msg = data.get('message', 'Unknown error')
                            logger.warning(f"FastSaver API error for format {attempt_format}: {error_msg}")
                            last_error = error_msg
                            continue  # Try next format

                        # Success - break out of loop
                        break

                except httpx.HTTPError as e:
                    logger.warning(f"HTTP error for format {attempt_format}: {e}")
                    last_error = str(e)
                    continue  # Try next format
            else:
                # All formats failed
                return DownloadResult(
                    success=False,
                    message=f"Download failed for all formats. Last error: {last_error}"
                )

            # Extract file_id from response
            file_id = data.get("file_id")
            if not file_id:
                return DownloadResult(
                    success=False,
                    message="No file_id returned from FastSaver API"
                )

            # Validate that the returned media type matches what we requested
            returned_media_type = data.get("media_type")
            if returned_media_type and returned_media_type != media_type:
                logger.warning(f"FastSaver API returned {returned_media_type} but we requested {media_type}")
                # This is just a warning, we'll still proceed with the file_id

            # Get video title for caption
            title = data.get("title")
            if not title:
                title = await self._get_video_title(video_id)

            logger.info(f"FastSaver download successful: file_id={file_id}, format: {params['format']}, returned_media_type: {returned_media_type}, title: {title}")

            return DownloadResult(
                success=True,
                message=f"YouTube {media_type} downloaded successfully via FastSaver API (format: {params['format']})",
                file_path=None,  # No local file path since we use file_id
                telegram_file_id=file_id,
                title=title  # Return title for caption
            )

        except httpx.HTTPError as e:
            logger.error(f"HTTP error downloading via FastSaver API: {e}")
            return DownloadResult(
                success=False,
                message=f"Network error: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Error downloading via FastSaver API: {e}")
            return DownloadResult(
                success=False,
                message=f"Error: {str(e)}"
            )
