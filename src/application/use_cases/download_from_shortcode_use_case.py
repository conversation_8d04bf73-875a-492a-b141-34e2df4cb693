"""Use case for downloading from YouTube shortcode."""
import logging

from src.application.dtos.search_dtos import (
    DownloadFromShortcodeRequest,
    DownloadFromShortcodeResponse
)
from src.domain.value_objects.identifiers import Cha<PERSON><PERSON><PERSON>, BotToken, UserId
from src.domain.interfaces.services import (
    IMusicSearchService,
    ITelegramService,
    IFileService
)
from src.domain.interfaces.repositories import IHistoryRepository

logger = logging.getLogger(__name__)


class DownloadFromShortcodeUseCase:
    """Use case for downloading media from YouTube shortcode."""

    def __init__(
        self,
        music_search_service: IMusicSearchService,
        telegram_service: ITelegramService,
        file_service: IFileService,
        history_repository: IHistoryRepository
    ):
        self._music_search_service = music_search_service
        self._telegram_service = telegram_service
        self._file_service = file_service
        self._history_repository = history_repository

    async def execute(self, request: DownloadFromShortcodeRequest) -> DownloadFromShortcodeResponse:
        """Execute the download from shortcode use case."""
        try:
            logger.info(f"Downloading from shortcode: {request.shortcode}, media_type: {request.media_type}")

            chat_id = ChatId(request.chat_id)
            bot_token = BotToken(request.bot_token)
            user_id = UserId(request.chat_id)  # Using chat_id as user_id for simplicity

            # Get the actual bot username from Telegram API
            try:
                bot_username = await self._telegram_service.get_bot_username_public()
            except Exception as e:
                logger.warning(f"Failed to get bot username: {e}, using fallback")
                bot_username = "instasaver_bot"

            # Download media using the music search service (now uses FastSaver API)
            download_result = await self._music_search_service.download_from_shortcode(
                shortcode=request.shortcode,
                media_type=request.media_type,
                bot_username=bot_username
            )

            if not download_result.success:
                await self._telegram_service.send_message(
                    chat_id,
                    f"Error downloading media: {download_result.message}"
                )
                return DownloadFromShortcodeResponse(
                    success=False,
                    message=download_result.message
                )

            # Send media to Telegram using file_id from FastSaver API
            file_id = None
            if request.media_type == "audio":
                file_id = await self._telegram_service.send_audio_by_file_id(
                    chat_id=chat_id,
                    file_id=download_result.telegram_file_id,
                    caption=download_result.title,
                    title=download_result.title
                )
            else:  # video
                file_id = await self._telegram_service.send_video_by_file_id(
                    chat_id=chat_id,
                    file_id=download_result.telegram_file_id,
                    caption=download_result.title
                )

            # Save to history (using shortcode as URL)
            from src.domain.value_objects.url import Url
            shortcode_url = Url(f"https://www.youtube.com/watch?v={request.shortcode}")
            await self._history_repository.save_download_history(
                user_id, shortcode_url, file_id
            )

            # No file cleanup needed since we're using file_id instead of local files

            return DownloadFromShortcodeResponse(
                success=True,
                message=f"Media downloaded and sent successfully",
                file_id=file_id
            )

        except Exception as e:
            logger.error(f"Error in download from shortcode use case: {e}")
            await self._telegram_service.send_message(
                ChatId(request.chat_id),
                f"Error downloading media: {str(e)}"
            )
            return DownloadFromShortcodeResponse(
                success=False,
                message=str(e)
            )
